import { useState, useEffect, useContext } from 'react'
import { DataContext } from '../../context/DataContext'
import ImageUploader from '../common/ImageUploader'

const CabinetsManagement = () => {
  const {
    cabinetsData: contextCabinets,
    addNewCabinet,
    updateExistingCabinet,
    removeCabinet,
    categories,
    refreshCabinets
  } = useContext(DataContext)

  const [cabinetsData, setCabinetsData] = useState([])
  const [showModal, setShowModal] = useState(false)
  const [editingCabinet, setEditingCabinet] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'modern',
    images: ['']
  })
  const [saving, setSaving] = useState(false)

  // Load cabinets data on component mount
  useEffect(() => {
    setCabinetsData(contextCabinets || [])
  }, [contextCabinets])

  // Get cabinet categories from database
  const cabinetCategories = categories.filter(cat => cat.type === 'cabinet')

  const getImageUrl = (image) => {
    if (!image) return null
    if (typeof image === 'string') return image
    if (typeof image === 'object' && image.image_url) return image.image_url
    return null
  }

  const getCategoryName = (categorySlug) => {
    const category = cabinetCategories.find(cat => cat.slug === categorySlug)
    return category ? category.name : categorySlug
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }



  const openModal = (cabinet = null) => {
    if (cabinet) {
      setEditingCabinet(cabinet)
      setFormData({
        title: cabinet.title,
        description: cabinet.description,
        category: cabinet.category_slug || cabinet.category,
        images: cabinet.images && cabinet.images.length > 0
          ? cabinet.images.map(img => typeof img === 'string' ? img : img.image_url || '')
          : ['']
      })
    } else {
      setEditingCabinet(null)
      setFormData({
        title: '',
        description: '',
        category: 'modern',
        images: ['']
      })
    }
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingCabinet(null)
    setFormData({
      title: '',
      description: '',
      category: 'modern',
      images: ['']
    })
  }

  const handleSave = async () => {
    // التحقق من وجود صورة واحدة على الأقل وتحديد الفئة
    const validImages = formData.images.filter(img => typeof img === 'string' && img.trim() !== '')
    if (validImages.length === 0) {
      alert('يرجى إضافة صورة واحدة على الأقل')
      return
    }

    if (!formData.category) {
      alert('يرجى تحديد فئة الخزانة')
      return
    }

    setSaving(true)
    try {
      // Find category ID
      const selectedCategory = cabinetCategories.find(cat => cat.slug === formData.category)

      // إنشاء عنوان تلقائي بناءً على الفئة
      const categoryName = selectedCategory?.name || formData.category
      const autoTitle = `خزانة ${categoryName}`

      const cabinetData = {
        title: formData.title.trim() || autoTitle,
        description: formData.description.trim() || '',
        category_id: selectedCategory ? selectedCategory.id : null,
        images: validImages,
        is_featured: false,
        sort_order: 0
      }

      if (editingCabinet) {
        await updateExistingCabinet(editingCabinet.id, cabinetData)
        alert('تم تحديث الخزانة بنجاح!')
      } else {
        await addNewCabinet(cabinetData)
        alert('تم إضافة الخزانة بنجاح!')
      }

      await refreshCabinets()
      closeModal()
    } catch (error) {
      console.error('خطأ في حفظ الخزانة:', error)
      alert('حدث خطأ أثناء الحفظ')
    } finally {
      setSaving(false)
    }
  }

  const deleteCabinet = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الخزانة؟')) {
      try {
        await removeCabinet(id)
        await refreshCabinets()
        alert('تم حذف الخزانة بنجاح!')
      } catch (error) {
        console.error('خطأ في حذف الخزانة:', error)
        alert('حدث خطأ أثناء الحذف')
      }
    }
  }



  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الخزائن</h1>
          <p className="text-gray-600 mt-1">إضافة وتعديل وحذف تصاميم الخزائن</p>
        </div>
        <button
          onClick={() => openModal()}
          className="btn-primary"
        >
          <i className="ri-add-line ml-2"></i>
          إضافة خزانة جديدة
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الخزائن</p>
              <p className="text-3xl font-bold text-gray-900">{cabinetsData?.length || 0}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i className="ri-archive-line text-2xl text-purple-600"></i>
            </div>
          </div>
        </div>
        {cabinetCategories.map(category => (
          <div key={category.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{category.name}</p>
                <p className="text-3xl font-bold text-gray-900">
                  {cabinetsData.filter(cabinet => cabinet.category_id === category.id).length}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i className="ri-layout-line text-2xl text-green-600"></i>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Cabinets Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {cabinetsData?.map((cabinet) => (
          <div key={cabinet.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <div className="relative h-48">
              <img
                src={getImageUrl(cabinet.images?.[0]) || 'https://via.placeholder.com/400x300?text=No+Image'}
                alt={cabinet.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-3 right-3">
                <span className="badge badge-info">{cabinet.category_name || getCategoryName(cabinet.category)}</span>
              </div>
              <div className="absolute top-3 left-3 flex space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => openModal(cabinet)}
                  className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors"
                >
                  <i className="ri-edit-line text-blue-600"></i>
                </button>
                <button
                  onClick={() => deleteCabinet(cabinet.id)}
                  className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors"
                >
                  <i className="ri-delete-bin-line text-red-600"></i>
                </button>
              </div>
            </div>
            <div className="p-6">
              <h3 className="font-bold text-gray-900 mb-2">{cabinet.title}</h3>
              <p className="text-gray-600 text-sm mb-4">{cabinet.description}</p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">
                  {cabinet.images?.length || 0} صورة
                </span>
                <div className="flex space-x-2 rtl:space-x-reverse">
                  {cabinet.images?.slice(0, 3).map((image, index) => (
                    <div key={index} className="w-8 h-8 rounded border-2 border-white shadow-sm overflow-hidden">
                      <img src={getImageUrl(image)} alt="" className="w-full h-full object-cover" />
                    </div>
                  ))}
                  {cabinet.images && cabinet.images.length > 3 && (
                    <div className="w-8 h-8 rounded border-2 border-white shadow-sm bg-gray-100 flex items-center justify-center">
                      <span className="text-xs text-gray-600">+{cabinet.images.length - 3}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {cabinetsData?.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-archive-line text-4xl text-gray-400"></i>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد خزائن</h3>
          <p className="text-gray-600 mb-6">ابدأ بإضافة أول تصميم خزانة</p>
          <button
            onClick={() => openModal()}
            className="btn-primary"
          >
            إضافة خزانة جديدة
          </button>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900">
                  {editingCabinet ? 'تعديل الخزانة' : 'إضافة خزانة جديدة'}
                </h2>
                <button
                  onClick={closeModal}
                  className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <i className="ri-close-line text-gray-600"></i>
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الخزانة (اختياري)
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className="form-input"
                  placeholder="أدخل عنوان الخزانة (سيتم إنشاء عنوان تلقائي إذا ترك فارغاً)"
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الوصف (اختياري)
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="form-textarea"
                  placeholder="أدخل وصف الخزانة (اختياري)"
                />
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفئة *
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="form-input"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الصور *
                </label>
                <ImageUploader
                  type="cabinet"
                  multiple={true}
                  maxFiles={10}
                  existingImages={formData.images.map(url => ({ url }))}
                  onImagesUploaded={(imageUrls) => {
                    setFormData({ ...formData, images: imageUrls })
                  }}
                />
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3 rtl:space-x-reverse">
              <button
                onClick={closeModal}
                className="btn-secondary"
                disabled={saving}
              >
                إلغاء
              </button>
              <button
                onClick={handleSave}
                className="btn-primary"
                disabled={saving}
              >
                {saving ? 'جاري الحفظ...' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CabinetsManagement
