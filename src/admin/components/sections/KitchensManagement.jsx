import { useState } from 'react'
import { useData } from '../../context/DataContext'

const KitchensManagement = () => {
  const {
    kitchensData,
    addNew<PERSON>itchen,
    updateExistingKitchen,
    removeKitchen,
    categories,
    loading,
    error
  } = useData()
  const [showModal, setShowModal] = useState(false)
  const [editingKitchen, setEditingKitchen] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'modern',
    categoryId: null,
    images: ['']
  })
  const [saving, setSaving] = useState(false)

  // Get kitchen categories from database
  const kitchenCategories = categories.filter(cat => cat.type === 'kitchen')

  const getImageUrl = (image) => {
    if (!image) return null
    if (typeof image === 'string') return image
    if (typeof image === 'object' && image.image_url) return image.image_url
    return null
  }



  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleImageChange = (index, value) => {
    const updatedImages = [...formData.images]
    updatedImages[index] = value
    setFormData({
      ...formData,
      images: updatedImages
    })
  }

  const addImageField = () => {
    setFormData({
      ...formData,
      images: [...formData.images, '']
    })
  }

  const removeImageField = (index) => {
    const updatedImages = formData.images.filter((_, i) => i !== index)
    setFormData({
      ...formData,
      images: updatedImages
    })
  }

  const handleImageUpload = (index, e) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        handleImageChange(index, e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const openModal = (kitchen = null) => {
    if (kitchen) {
      setEditingKitchen(kitchen)
      setFormData({
        title: kitchen.title,
        description: kitchen.description,
        category: kitchen.category,
        images: kitchen.images && kitchen.images.length > 0
          ? kitchen.images.map(img => typeof img === 'string' ? img : img.image_url || '')
          : ['']
      })
    } else {
      setEditingKitchen(null)
      setFormData({
        title: '',
        description: '',
        category: 'modern',
        images: ['']
      })
    }
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingKitchen(null)
    setFormData({
      title: '',
      description: '',
      category: 'modern',
      images: ['']
    })
  }

  const handleSave = async () => {
    if (!formData.title.trim() || !formData.description.trim()) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const validImages = formData.images.filter(img => typeof img === 'string' && img.trim() !== '')
    if (validImages.length === 0) {
      alert('يرجى إضافة صورة واحدة على الأقل')
      return
    }

    setSaving(true)
    try {
      // Find category ID from selected category
      const selectedCategory = kitchenCategories.find(cat => cat.slug === formData.category)

      const kitchenData = {
        title: formData.title,
        description: formData.description,
        categoryId: selectedCategory?.id,
        category: formData.category,
        images: validImages,
        isFeatured: false
      }

      if (editingKitchen) {
        // تحديث مطبخ موجود
        await updateExistingKitchen(editingKitchen.id, kitchenData)
      } else {
        // إضافة مطبخ جديد
        await addNewKitchen(kitchenData)
      }

      closeModal()
      alert('تم حفظ المطبخ بنجاح!')
    } catch (error) {
      alert(error.message || 'حدث خطأ أثناء الحفظ')
    } finally {
      setSaving(false)
    }
  }

  const deleteKitchen = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المطبخ؟')) {
      try {
        await removeKitchen(id)
        alert('تم حذف المطبخ بنجاح!')
      } catch (error) {
        alert(error.message || 'حدث خطأ أثناء الحذف')
      }
    }
  }

  const getCategoryName = (categorySlug) => {
    const category = kitchenCategories.find(cat => cat.slug === categorySlug)
    return category ? category.name : categorySlug
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة المطابخ</h1>
          <p className="text-gray-600 mt-1">إضافة وتعديل وحذف تصاميم المطابخ</p>
        </div>
        <button
          onClick={() => openModal()}
          className="btn-primary"
        >
          <i className="ri-add-line ml-2"></i>
          إضافة مطبخ جديد
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي المطابخ</p>
              <p className="text-3xl font-bold text-gray-900">{kitchensData.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="ri-home-4-line text-2xl text-blue-600"></i>
            </div>
          </div>
        </div>
        {categories.map(category => (
          <div key={category.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{category.name}</p>
                <p className="text-3xl font-bold text-gray-900">
                  {kitchensData.filter(kitchen => kitchen.category === category.id).length}
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i className="ri-layout-line text-2xl text-purple-600"></i>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Kitchens Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {kitchensData?.map((kitchen) => (
          <div key={kitchen.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <div className="relative h-48">
              <img
                src={getImageUrl(kitchen.images?.[0]) || 'https://via.placeholder.com/400x300?text=No+Image'}
                alt={kitchen.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-3 right-3">
                <span className="badge badge-info">{getCategoryName(kitchen.category)}</span>
              </div>
              <div className="absolute top-3 left-3 flex space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => openModal(kitchen)}
                  className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors"
                >
                  <i className="ri-edit-line text-blue-600"></i>
                </button>
                <button
                  onClick={() => deleteKitchen(kitchen.id)}
                  className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors"
                >
                  <i className="ri-delete-bin-line text-red-600"></i>
                </button>
              </div>
            </div>
            <div className="p-6">
              <h3 className="font-bold text-gray-900 mb-2">{kitchen.title}</h3>
              <p className="text-gray-600 text-sm mb-4">{kitchen.description}</p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">
                  {kitchen.images?.length || 0} صورة
                </span>
                <div className="flex space-x-2 rtl:space-x-reverse">
                  {kitchen.images?.slice(0, 3).map((image, index) => (
                    <div key={index} className="w-8 h-8 rounded border-2 border-white shadow-sm overflow-hidden">
                      <img src={getImageUrl(image)} alt="" className="w-full h-full object-cover" />
                    </div>
                  ))}
                  {kitchen.images.length > 3 && (
                    <div className="w-8 h-8 rounded border-2 border-white shadow-sm bg-gray-100 flex items-center justify-center">
                      <span className="text-xs text-gray-600">+{kitchen.images.length - 3}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {kitchensData?.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-home-4-line text-4xl text-gray-400"></i>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مطابخ</h3>
          <p className="text-gray-600 mb-6">ابدأ بإضافة أول تصميم مطبخ</p>
          <button
            onClick={() => openModal()}
            className="btn-primary"
          >
            إضافة مطبخ جديد
          </button>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900">
                  {editingKitchen ? 'تعديل المطبخ' : 'إضافة مطبخ جديد'}
                </h2>
                <button
                  onClick={closeModal}
                  className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <i className="ri-close-line text-gray-600"></i>
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان المطبخ *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className="form-input"
                  placeholder="أدخل عنوان المطبخ"
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الوصف *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="form-textarea"
                  placeholder="أدخل وصف المطبخ"
                />
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفئة *
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="form-input"
                >
                  {kitchenCategories.map(category => (
                    <option key={category.id} value={category.slug}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الصور *
                </label>
                <div className="space-y-4">
                  {formData.images.map((image, index) => (
                    <div key={index} className="flex space-x-3 rtl:space-x-reverse">
                      <div className="flex-1">
                        <input
                          type="url"
                          value={image}
                          onChange={(e) => handleImageChange(index, e.target.value)}
                          className="form-input"
                          placeholder="رابط الصورة"
                        />
                      </div>
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleImageUpload(index, e)}
                          className="hidden"
                          id={`image-upload-${index}`}
                        />
                        <label
                          htmlFor={`image-upload-${index}`}
                          className="btn-secondary cursor-pointer"
                        >
                          <i className="ri-upload-line"></i>
                        </label>
                        {formData.images.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeImageField(index)}
                            className="btn-danger"
                          >
                            <i className="ri-delete-bin-line"></i>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addImageField}
                    className="btn-secondary w-full"
                  >
                    <i className="ri-add-line ml-2"></i>
                    إضافة صورة أخرى
                  </button>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3 rtl:space-x-reverse">
              <button
                onClick={closeModal}
                className="btn-secondary"
                disabled={saving}
              >
                إلغاء
              </button>
              <button
                onClick={handleSave}
                className="btn-primary"
                disabled={saving}
              >
                {saving ? 'جاري الحفظ...' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default KitchensManagement
