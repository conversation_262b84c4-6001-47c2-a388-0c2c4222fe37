import { useState, useRef } from 'react'

const ImageUploader = ({ 
  onImagesUploaded, 
  type = 'kitchen', 
  multiple = true, 
  maxFiles = 10,
  existingImages = [] 
}) => {
  const [uploading, setUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [uploadedImages, setUploadedImages] = useState(existingImages)
  const fileInputRef = useRef(null)

  const handleFiles = async (files) => {
    if (!files || files.length === 0) return

    setUploading(true)
    try {
      const formData = new FormData()
      
      // إضافة الملفات إلى FormData
      Array.from(files).forEach(file => {
        formData.append('images', file)
      })
      formData.append('type', type)

      const response = await fetch('http://localhost:3002/api/upload/images', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('فشل في رفع الصور')
      }

      const result = await response.json()
      
      if (result.success) {
        const newImages = [...uploadedImages, ...result.images]
        setUploadedImages(newImages)
        
        // إرسال URLs للمكون الأب
        const imageUrls = newImages.map(img => img.url)
        onImagesUploaded(imageUrls)
      }
    } catch (error) {
      console.error('Error uploading images:', error)
      alert('فشل في رفع الصور: ' + error.message)
    } finally {
      setUploading(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    setDragActive(false)
    
    const files = e.dataTransfer.files
    handleFiles(files)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setDragActive(false)
  }

  const handleFileSelect = (e) => {
    const files = e.target.files
    handleFiles(files)
  }

  const removeImage = async (index) => {
    const imageToRemove = uploadedImages[index]
    
    try {
      // حذف من الخادم إذا كانت صورة مرفوعة محلياً
      if (imageToRemove.filename) {
        await fetch(`http://localhost:3002/api/upload/image/${imageToRemove.filename}?type=${type}`, {
          method: 'DELETE'
        })
      }
      
      // حذف من القائمة المحلية
      const newImages = uploadedImages.filter((_, i) => i !== index)
      setUploadedImages(newImages)
      
      // تحديث المكون الأب
      const imageUrls = newImages.map(img => img.url)
      onImagesUploaded(imageUrls)
    } catch (error) {
      console.error('Error removing image:', error)
      alert('فشل في حذف الصورة')
    }
  }

  return (
    <div className="space-y-4">
      {/* منطقة رفع الصور */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="space-y-4">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
            <i className="ri-upload-cloud-line text-2xl text-gray-400"></i>
          </div>
          
          <div>
            <p className="text-lg font-medium text-gray-900">
              اسحب الصور هنا أو انقر للاختيار
            </p>
            <p className="text-sm text-gray-500 mt-1">
              PNG, JPG, WebP حتى 10MB لكل صورة
            </p>
          </div>
          
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="btn-primary"
          >
            {uploading ? (
              <>
                <i className="ri-loader-4-line animate-spin mr-2"></i>
                جاري الرفع...
              </>
            ) : (
              <>
                <i className="ri-add-line mr-2"></i>
                اختيار الصور
              </>
            )}
          </button>
        </div>
      </div>

      {/* عرض الصور المرفوعة */}
      {uploadedImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {uploadedImages.map((image, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={image.thumbnail || image.url}
                  alt={`صورة ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* زر الحذف */}
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              >
                <i className="ri-close-line text-sm"></i>
              </button>
              
              {/* معلومات الصورة */}
              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                {image.size && (
                  <p>{(image.size / 1024).toFixed(1)} KB</p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* عداد الصور */}
      {uploadedImages.length > 0 && (
        <div className="text-sm text-gray-500 text-center">
          {uploadedImages.length} من {maxFiles} صورة
        </div>
      )}
    </div>
  )
}

export default ImageUploader
