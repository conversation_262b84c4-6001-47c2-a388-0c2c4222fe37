// نظام رفع الملفات المحسن - <PERSON><PERSON> & Sharp
// Enhanced File Upload System - <PERSON><PERSON> & Sharp

const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

// إنشاء مجلدات التخزين
const createUploadDirs = async () => {
  const dirs = [
    'uploads',
    'uploads/kitchens',
    'uploads/cabinets',
    'uploads/temp'
  ];
  
  for (const dir of dirs) {
    try {
      await fs.mkdir(path.join(__dirname, '..', dir), { recursive: true });
    } catch (error) {
      console.error(`Error creating directory ${dir}:`, error);
    }
  }
};

// إعداد Multer للتخزين المؤقت
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  // قبول الصور فقط
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('يُسمح برفع الصور فقط!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB حد أقصى
    files: 10 // حد أقصى 10 ملفات
  }
});

// دالة معالجة وضغط الصور
const processImage = async (buffer, filename, type = 'kitchen') => {
  try {
    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const extension = '.webp'; // استخدام WebP للضغط الأفضل
    const finalFilename = `${type}_${timestamp}_${randomString}${extension}`;
    
    // مسار الحفظ
    const uploadDir = path.join(__dirname, '..', 'uploads', `${type}s`);
    const filePath = path.join(uploadDir, finalFilename);
    
    // معالجة الصورة بـ Sharp
    await sharp(buffer)
      .resize(1200, 800, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .webp({ 
        quality: 85,
        effort: 4 
      })
      .toFile(filePath);
    
    // إنشاء thumbnail
    const thumbnailFilename = `thumb_${finalFilename}`;
    const thumbnailPath = path.join(uploadDir, thumbnailFilename);
    
    await sharp(buffer)
      .resize(400, 300, { 
        fit: 'cover' 
      })
      .webp({ 
        quality: 80 
      })
      .toFile(thumbnailPath);
    
    return {
      filename: finalFilename,
      thumbnail: thumbnailFilename,
      path: `/uploads/${type}s/${finalFilename}`,
      thumbnailPath: `/uploads/${type}s/${thumbnailFilename}`,
      size: (await fs.stat(filePath)).size
    };
    
  } catch (error) {
    console.error('Error processing image:', error);
    throw new Error('فشل في معالجة الصورة');
  }
};

// دالة حذف الصورة
const deleteImage = async (filename, type = 'kitchen') => {
  try {
    const uploadDir = path.join(__dirname, '..', 'uploads', `${type}s`);
    const filePath = path.join(uploadDir, filename);
    const thumbnailPath = path.join(uploadDir, `thumb_${filename}`);
    
    // حذف الصورة الأصلية والـ thumbnail
    await Promise.allSettled([
      fs.unlink(filePath),
      fs.unlink(thumbnailPath)
    ]);
    
    return true;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
};

// Middleware لرفع صور متعددة
const uploadMultiple = upload.array('images', 10);

// Middleware لرفع صورة واحدة
const uploadSingle = upload.single('image');

module.exports = {
  createUploadDirs,
  uploadMultiple,
  uploadSingle,
  processImage,
  deleteImage
};
